@echo off
echo Qwen 2.5 32B Instruct Model Download Script
echo ==========================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed or not in PATH.
    echo Please install Python from https://python.org
    echo Make sure to check "Add Python to PATH" during installation.
    pause
    exit /b 1
)

echo Python found. Starting download process...
echo.

REM Install requirements
echo Installing required packages...
python -m pip install --upgrade pip
python -m pip install -r requirements.txt

if %errorlevel% neq 0 (
    echo Failed to install requirements.
    pause
    exit /b 1
)

echo.
echo Starting model download...
echo WARNING: This will download approximately 65GB of data.
echo Make sure you have sufficient disk space and a stable internet connection.
echo.
set /p confirm="Continue with download? (y/N): "
if /i not "%confirm%"=="y" (
    echo Download cancelled.
    pause
    exit /b 0
)

REM Run the download script
python download_qwen_model.py

echo.
echo Download process completed.
echo Check the output above for any errors.
pause
