#!/usr/bin/env python3
"""
Download Qwen 2.5 32B Instruct model from Hugging Face
"""

import os
import sys
from pathlib import Path

def install_requirements():
    """Install required packages"""
    try:
        import subprocess
        
        packages = [
            "huggingface_hub",
            "transformers>=4.37.0",
            "torch",
            "accelerate",
            "safetensors"
        ]
        
        for package in packages:
            print(f"Installing {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            
    except Exception as e:
        print(f"Error installing packages: {e}")
        return False
    return True

def download_model():
    """Download the Qwen 2.5 32B Instruct model"""
    try:
        from huggingface_hub import snapshot_download
        
        model_name = "Qwen/Qwen2.5-32B-Instruct"
        local_dir = "./qwen2.5-32b-instruct"
        
        print(f"Downloading {model_name} to {local_dir}...")
        print("This is a large model (~65GB), so it will take some time...")
        
        # Create local directory
        Path(local_dir).mkdir(exist_ok=True)
        
        # Download the model
        snapshot_download(
            repo_id=model_name,
            local_dir=local_dir,
            local_dir_use_symlinks=False,
            resume_download=True
        )
        
        print(f"Model downloaded successfully to: {os.path.abspath(local_dir)}")
        return True
        
    except Exception as e:
        print(f"Error downloading model: {e}")
        return False

def create_usage_example():
    """Create a simple usage example script"""
    example_code = '''#!/usr/bin/env python3
"""
Example usage of Qwen 2.5 32B Instruct model
"""

from transformers import AutoModelForCausalLM, AutoTokenizer
import torch

def load_model():
    """Load the Qwen model and tokenizer"""
    model_path = "./qwen2.5-32b-instruct"
    
    print("Loading tokenizer...")
    tokenizer = AutoTokenizer.from_pretrained(model_path)
    
    print("Loading model... (this may take a while)")
    model = AutoModelForCausalLM.from_pretrained(
        model_path,
        torch_dtype=torch.bfloat16,  # Use bfloat16 for better performance
        device_map="auto",  # Automatically distribute across available GPUs
        trust_remote_code=True
    )
    
    return model, tokenizer

def generate_response(model, tokenizer, prompt):
    """Generate a response using the model"""
    messages = [
        {"role": "system", "content": "You are Qwen, created by Alibaba Cloud. You are a helpful assistant."},
        {"role": "user", "content": prompt}
    ]
    
    # Apply chat template
    text = tokenizer.apply_chat_template(
        messages,
        tokenize=False,
        add_generation_prompt=True
    )
    
    # Tokenize input
    model_inputs = tokenizer([text], return_tensors="pt").to(model.device)
    
    # Generate response
    with torch.no_grad():
        generated_ids = model.generate(
            **model_inputs,
            max_new_tokens=512,
            do_sample=True,
            temperature=0.7,
            top_p=0.8,
            pad_token_id=tokenizer.eos_token_id
        )
    
    # Decode response
    generated_ids = [
        output_ids[len(input_ids):] for input_ids, output_ids in zip(model_inputs.input_ids, generated_ids)
    ]
    
    response = tokenizer.batch_decode(generated_ids, skip_special_tokens=True)[0]
    return response

def main():
    """Main function"""
    try:
        # Load model and tokenizer
        model, tokenizer = load_model()
        
        print("\\nModel loaded successfully!")
        print("You can now chat with Qwen 2.5 32B Instruct")
        print("Type 'quit' to exit\\n")
        
        while True:
            user_input = input("You: ").strip()
            if user_input.lower() in ['quit', 'exit', 'q']:
                break
                
            if user_input:
                print("Qwen: ", end="", flush=True)
                response = generate_response(model, tokenizer, user_input)
                print(response)
                print()
    
    except Exception as e:
        print(f"Error: {e}")
        print("Make sure you have downloaded the model first using download_qwen_model.py")

if __name__ == "__main__":
    main()
'''
    
    with open("qwen_chat_example.py", "w", encoding="utf-8") as f:
        f.write(example_code)
    
    print("Created usage example: qwen_chat_example.py")

def main():
    """Main function"""
    print("Qwen 2.5 32B Instruct Model Downloader")
    print("=" * 50)
    
    # Install requirements
    print("Step 1: Installing required packages...")
    if not install_requirements():
        print("Failed to install requirements. Please install manually:")
        print("pip install huggingface_hub transformers torch accelerate safetensors")
        return
    
    # Download model
    print("\nStep 2: Downloading model...")
    if not download_model():
        print("Failed to download model.")
        return
    
    # Create usage example
    print("\nStep 3: Creating usage example...")
    create_usage_example()
    
    print("\n" + "=" * 50)
    print("Setup complete!")
    print("\nNext steps:")
    print("1. Run 'python qwen_chat_example.py' to start chatting with the model")
    print("2. Make sure you have sufficient GPU memory (recommended: 24GB+ VRAM)")
    print("3. For CPU-only usage, the model will be very slow")
    
    print(f"\nModel location: {os.path.abspath('./qwen2.5-32b-instruct')}")

if __name__ == "__main__":
    main()
