# Qwen 2.5 32B Instruct Model Setup

This directory contains scripts to download and use the Qwen 2.5 32B Instruct model.

## Model Information

- **Model**: Qwen/Qwen2.5-32B-Instruct
- **Size**: ~65GB download
- **Parameters**: 32.5B
- **Context Length**: 131,072 tokens
- **Languages**: English + 28 other languages
- **License**: Apache 2.0

## Prerequisites

1. **Python 3.8+** installed and added to PATH
2. **65GB+ free disk space**
3. **Stable internet connection**
4. **Recommended**: 24GB+ GPU VRAM for optimal performance

## Quick Start

### Option 1: Using the Batch Script (Windows)
```bash
# Double-click download_model.bat or run in command prompt:
download_model.bat
```

### Option 2: Manual Installation
```bash
# Install requirements
pip install -r requirements.txt

# Download the model
python download_qwen_model.py

# Run the chat example
python qwen_chat_example.py
```

## Files Description

- `download_qwen_model.py` - Main download script
- `qwen_chat_example.py` - Example usage script (created after download)
- `requirements.txt` - Python dependencies
- `download_model.bat` - Windows batch script for easy setup
- `README.md` - This file

## System Requirements

### Minimum Requirements
- **RAM**: 32GB+ system RAM
- **Storage**: 65GB+ free space
- **GPU**: Optional but recommended (24GB+ VRAM)

### Recommended Requirements
- **RAM**: 64GB+ system RAM
- **GPU**: NVIDIA RTX 4090, A100, or similar (24GB+ VRAM)
- **Storage**: SSD with 100GB+ free space

## Usage Examples

### Basic Chat
```python
from transformers import AutoModelForCausalLM, AutoTokenizer

# Load model
model = AutoModelForCausalLM.from_pretrained("./qwen2.5-32b-instruct")
tokenizer = AutoTokenizer.from_pretrained("./qwen2.5-32b-instruct")

# Generate response
messages = [{"role": "user", "content": "Hello!"}]
text = tokenizer.apply_chat_template(messages, tokenize=False, add_generation_prompt=True)
inputs = tokenizer([text], return_tensors="pt")
outputs = model.generate(**inputs, max_new_tokens=512)
response = tokenizer.decode(outputs[0], skip_special_tokens=True)
```

### Advanced Configuration
```python
# For better performance with limited VRAM
model = AutoModelForCausalLM.from_pretrained(
    "./qwen2.5-32b-instruct",
    torch_dtype=torch.bfloat16,
    device_map="auto",
    load_in_8bit=True  # Enable 8-bit quantization
)
```

## Troubleshooting

### Common Issues

1. **Python not found**
   - Install Python from https://python.org
   - Make sure "Add Python to PATH" is checked during installation

2. **Out of memory errors**
   - Use quantization: `load_in_8bit=True` or `load_in_4bit=True`
   - Reduce batch size
   - Use CPU inference (slower): `device_map="cpu"`

3. **Download interrupted**
   - The script supports resume download
   - Simply run the download script again

4. **Slow inference**
   - Use GPU if available
   - Enable quantization
   - Reduce max_new_tokens

### Performance Tips

1. **GPU Usage**: Ensure CUDA is properly installed
2. **Memory**: Close other applications to free up RAM
3. **Storage**: Use SSD for better loading times
4. **Quantization**: Use 8-bit or 4-bit quantization for lower memory usage

## Model Capabilities

The Qwen 2.5 32B Instruct model excels at:

- **Conversational AI**: Natural dialogue and chat
- **Code Generation**: Programming in multiple languages
- **Mathematical Reasoning**: Complex problem solving
- **Text Analysis**: Summarization, translation, analysis
- **Creative Writing**: Stories, poems, creative content
- **Question Answering**: Factual and reasoning-based questions

## License

This model is released under the Apache 2.0 license. See the original model page for details:
https://huggingface.co/Qwen/Qwen2.5-32B-Instruct

## Support

For issues with the model itself, visit:
- Hugging Face: https://huggingface.co/Qwen/Qwen2.5-32B-Instruct
- GitHub: https://github.com/QwenLM/Qwen2.5

For issues with these scripts, check the error messages and ensure all prerequisites are met.
